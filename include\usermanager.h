#ifndef USERMANAGER_H
#define USERMANAGER_H

#include "user.h"
#include <map>
#include <string>
#include <memory>

class UserManager {
private:
    std::map<std::string, std::shared_ptr<User>> users;
    std::shared_ptr<User> currentUser;
    std::string dataFile;
    
public:
    UserManager(const std::string& dataFile = "users.dat");
    ~UserManager();
    
    // 用户管理
    bool registerUser(const std::string& username, const std::string& password);
    bool loginUser(const std::string& username, const std::string& password);
    void logoutUser();
    
    // 当前用户
    std::shared_ptr<User> getCurrentUser() const { return currentUser; }
    bool isLoggedIn() const { return currentUser != nullptr; }
    
    // 数据持久化
    void saveUsers();
    void loadUsers();
    
private:
    bool isValidUsername(const std::string& username) const;
    bool isValidPassword(const std::string& password) const;
};

#endif

# 个人银行账户管理系统 - 增强版

## 项目概述

这是一个增强版的个人银行账户管理系统，在原有功能基础上添加了异常处理机制、用户管理系统、友好界面以及高级查询功能。

## 主要改进

### 完善1：异常处理机制

- **日期异常处理**：在Date类构造函数和read()方法中添加了输入验证，对无效日期抛出`std::runtime_error`异常
- **账户异常处理**：创建了`AccountException`类，继承自`std::runtime_error`，用于处理账户操作错误（如余额不足、信用额度超限等）
- **统一异常处理**：在主程序中统一捕获和处理异常，避免程序崩溃并提供友好的错误信息

### 完善2：用户管理与界面增强

- **用户注册登录系统**：支持多用户使用，每个用户有独立的账户管理
- **友好的操作界面**：提供清晰的菜单系统和操作提示
- **用户提醒功能**：
  - 信用卡还款提醒
  - 余额过低提醒
  - 月度账户统计
- **高级查询功能**：
  - 按时间排序查询
  - 按交易金额排序查询（从大到小）
- **月度统计**：收入支出统计和净收入计算

## 文件结构

```
BankProject/
├── include/                 # 头文件目录
│   ├── account.h           # 账户基类（含异常类）
│   ├── accountrecord.h     # 账目记录类
│   ├── accumulator.h       # 累加器类
│   ├── bankinterface.h     # 银行界面类
│   ├── creditaccount.h     # 信用账户类
│   ├── date.h              # 日期类
│   ├── savingsaccount.h    # 储蓄账户类
│   ├── user.h              # 用户类
│   └── usermanager.h       # 用户管理类
├── src/                    # 源文件目录
│   ├── account.cpp         # 账户基类实现
│   ├── accountrecord.cpp   # 账目记录实现
│   ├── accumulator.cpp     # 累加器实现
│   ├── bankinterface.cpp   # 银行界面实现
│   ├── creditaccount.cpp   # 信用账户实现
│   ├── date.cpp            # 日期类实现
│   ├── main_enhanced.cpp   # 增强版主程序
│   ├── savingsaccount.cpp  # 储蓄账户实现
│   ├── step_5.cpp          # 原版主程序
│   ├── user.cpp            # 用户类实现
│   └── usermanager.cpp     # 用户管理实现
├── build/                  # 编译输出目录
├── Makefile               # 编译配置文件
├── README.md              # 项目说明文档
└── sample_commands.txt    # 示例命令文件
```

## 编译和运行

### 编译增强版系统

```bash
make enhanced
```

### 编译原版系统

```bash
make original
```

### 运行程序

```bash
# 运行增强版
./build/bank_enhanced.exe

# 运行原版
./build/step_5.exe
```

### 清理编译文件

```bash
make clean
```

## 使用说明

### 增强版系统使用

1. **首次使用**：
   - 运行程序后选择"注册"创建新用户
   - 用户名：3-20字符，仅支持字母数字下划线
   - 密码：6-50字符

2. **登录系统**：
   - 输入用户名和密码登录

3. **创建账户**：
   - 储蓄账户：需要设置年利率
   - 信用账户：需要设置信用额度、日利率和年费

4. **日常操作**：
   - 存款/取款：选择账户并输入金额和描述
   - 查看账户：显示所有账户信息
   - 账户统计：显示总资产和分类统计

5. **查询功能**：
   - 普通查询：按日期范围查询记录
   - 时间排序：按时间顺序显示记录
   - 金额排序：按交易金额从大到小排序

6. **月度管理**：
   - 更改日期：设置当前日期
   - 下个月：进入下个月并自动结算
   - 月度统计：查看收入支出统计

## 异常处理示例

系统会自动处理以下异常情况：

- **无效日期**：如输入2024/13/32，系统会提示"Invalid date: month out of range"
- **余额不足**：储蓄账户取款超过余额时，抛出AccountException
- **信用额度超限**：信用账户透支超过额度时，抛出AccountException
- **无效输入**：日期格式错误时，提示正确格式

## 多用户支持

- 每个用户拥有独立的账户空间
- 用户数据自动保存到users.dat文件
- 支持多用户同时注册，分别登录使用

## 提醒功能

登录后系统会自动显示：
- 需要还款的信用账户
- 余额过低的账户（低于100元）
- 当前总资产情况

## 技术特性

- **面向对象设计**：使用继承、多态、封装等OOP特性
- **STL容器**：使用vector管理账户，multimap管理历史记录
- **异常安全**：完整的异常处理机制
- **内存管理**：使用智能指针管理内存
- **跨平台**：支持Windows和Linux系统

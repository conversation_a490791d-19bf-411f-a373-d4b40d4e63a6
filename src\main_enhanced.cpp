// main_enhanced.cpp - 增强版银行账户管理系统主程序

#include "bankinterface.h"
#include <iostream>
#include <stdexcept>

using namespace std;

int main() {
    try {
        BankInterface interface;
        interface.run();
    } catch (const exception& e) {
        cout << "系统发生严重错误: " << e.what() << endl;
        cout << "程序将退出。" << endl;
        return 1;
    } catch (...) {
        cout << "系统发生未知错误，程序将退出。" << endl;
        return 1;
    }
    
    return 0;
}

#include "user.h"
#include "accountrecord.h"
#include "creditaccount.h"
#include <sstream>
#include <algorithm>

User::User(const std::string& username, const std::string& password) 
    : username(username), password(password) {
}

bool User::authenticate(const std::string& password) const {
    return this->password == password;
}

void User::addAccount(std::shared_ptr<Account> account) {
    accounts.push_back(account);
}

std::shared_ptr<Account> User::getAccount(size_t index) const {
    if (index < accounts.size()) {
        return accounts[index];
    }
    return nullptr;
}

double User::getTotalBalance() const {
    double total = 0.0;
    for (const auto& account : accounts) {
        total += account->getBalance();
    }
    return total;
}

double User::getMonthlyIncome(int year, int month) const {
    // 这里需要访问账户记录来计算月收入
    // 由于当前设计限制，我们返回一个简化的计算
    double income = 0.0;
    for (const auto& account : accounts) {
        if (account->getBalance() > 0) {
            income += account->getBalance() * 0.1; // 简化计算
        }
    }
    return income;
}

double User::getMonthlyExpense(int year, int month) const {
    // 这里需要访问账户记录来计算月支出
    // 由于当前设计限制，我们返回一个简化的计算
    double expense = 0.0;
    for (const auto& account : accounts) {
        if (account->getBalance() < 0) {
            expense += -account->getBalance() * 0.1; // 简化计算
        }
    }
    return expense;
}

std::vector<std::string> User::getReminders() const {
    std::vector<std::string> reminders;
    
    for (const auto& account : accounts) {
        // 检查信用卡账户的还款提醒
        auto creditAccount = std::dynamic_pointer_cast<CreditAccount>(account);
        if (creditAccount && creditAccount->getBalance() < 0) {
            std::ostringstream oss;
            oss << "账户 " << account->getId() << " 需要还款: " 
                << -creditAccount->getBalance() << " 元";
            reminders.push_back(oss.str());
        }
        
        // 检查余额过低提醒
        if (account->getBalance() < 100 && account->getBalance() >= 0) {
            std::ostringstream oss;
            oss << "账户 " << account->getId() << " 余额较低: " 
                << account->getBalance() << " 元";
            reminders.push_back(oss.str());
        }
    }
    
    return reminders;
}

std::string User::serialize() const {
    std::ostringstream oss;
    oss << username << "|" << password << "|" << accounts.size();
    // 注意：这里简化了账户序列化，实际应用中需要更复杂的序列化
    return oss.str();
}

User User::deserialize(const std::string& data) {
    std::istringstream iss(data);
    std::string username, password;
    size_t accountCount;
    
    std::getline(iss, username, '|');
    std::getline(iss, password, '|');
    iss >> accountCount;
    
    User user(username, password);
    // 注意：这里简化了账户反序列化
    return user;
}

#ifndef DATE_H
#define DATE_H
#include <iostream>
#include <stdexcept>
class Date {
private:
    int year;
    int month;
    int day;
    int totalDays() const;// 计算该日期是从1月1日开始的第几天
public:
    Date(int year = 1, int month = 1, int day = 1); // 添加默认参数，相当于默认构造函数
    int getYear() const { return year; }
    int getMonth() const { return month; }
    int getDay() const { return day; }
    int getMaxDay() const; // Get the maximum day for the current month
    int operator-(const Date& date) const;
    bool operator<(const Date& date) const; // 添加小于运算符重载，用于multimap
    void show() const;
    // 从标准输入读取日期
    static Date read();
};

#endif
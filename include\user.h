#ifndef USER_H
#define USER_H

#include <string>
#include <vector>
#include <memory>
#include "account.h"

class User {
private:
    std::string username;
    std::string password;
    std::vector<std::shared_ptr<Account>> accounts;
    
public:
    User(const std::string& username, const std::string& password);
    
    // 认证相关
    bool authenticate(const std::string& password) const;
    std::string getUsername() const { return username; }
    
    // 账户管理
    void addAccount(std::shared_ptr<Account> account);
    const std::vector<std::shared_ptr<Account>>& getAccounts() const { return accounts; }
    std::shared_ptr<Account> getAccount(size_t index) const;
    size_t getAccountCount() const { return accounts.size(); }
    
    // 统计功能
    double getTotalBalance() const;
    double getMonthlyIncome(int year, int month) const;
    double getMonthlyExpense(int year, int month) const;
    
    // 提醒功能
    std::vector<std::string> getReminders() const;
    
    // 序列化
    std::string serialize() const;
    static User deserialize(const std::string& data);
};

#endif

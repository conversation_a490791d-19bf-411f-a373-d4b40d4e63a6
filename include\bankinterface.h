#ifndef BANKINTERFACE_H
#define BANKINTERFACE_H

#include "usermanager.h"
#include "date.h"
#include <vector>
#include <memory>

class BankInterface {
private:
    UserManager userManager;
    Date currentDate;
    
public:
    BankInterface();
    
    // 主界面
    void run();
    
    // 登录/注册界面
    void showLoginMenu();
    bool handleLogin();
    bool handleRegister();
    
    // 主菜单
    void showMainMenu();
    void handleMainMenu();
    
    // 账户操作
    void createAccount();
    void depositMoney();
    void withdrawMoney();
    void showAccounts();
    void showAccountStatistics();
    
    // 查询功能
    void queryRecords();
    void queryRecordsByTime();
    void queryRecordsByAmount();
    
    // 日期操作
    void changeDate();
    void nextMonth();
    
    // 工具函数
    void showUserReminders();
    void showMonthlyStatistics();
    void clearScreen();
    void pauseScreen();
    
private:
    void handleException(const std::exception& e);
};

#endif

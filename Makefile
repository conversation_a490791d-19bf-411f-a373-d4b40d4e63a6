# Makefile for Enhanced Bank Account Management System

CXX = g++
CXXFLAGS = -std=c++11 -Wall -Wextra -Iinclude
SRCDIR = src
INCDIR = include
BUILDDIR = build

# Source files
SOURCES = $(SRCDIR)/account.cpp \
          $(SRCDIR)/accountrecord.cpp \
          $(SRCDIR)/accumulator.cpp \
          $(SRCDIR)/date.cpp \
          $(SRCDIR)/savingsaccount.cpp \
          $(SRCDIR)/creditaccount.cpp \
          $(SRCDIR)/user.cpp \
          $(SRCDIR)/usermanager.cpp \
          $(SRCDIR)/bankinterface.cpp

# Object files
OBJECTS = $(SOURCES:$(SRCDIR)/%.cpp=$(BUILDDIR)/%.o)

# Targets
ENHANCED_TARGET = $(BUILDDIR)/bank_enhanced.exe
ORIGINAL_TARGET = $(BUILDDIR)/step_5.exe

.PHONY: all clean enhanced original

all: enhanced

enhanced: $(ENHANCED_TARGET)

original: $(ORIGINAL_TARGET)

$(ENHANCED_TARGET): $(OBJECTS) $(BUILDDIR)/main_enhanced.o
	$(CXX) $(CXXFLAGS) -o $@ $^

$(ORIGINAL_TARGET): $(OBJECTS) $(BUILDDIR)/step_5.o
	$(CXX) $(CXXFLAGS) -o $@ $^

$(BUILDDIR)/%.o: $(SRCDIR)/%.cpp | $(BUILDDIR)
	$(CXX) $(CXXFLAGS) -c $< -o $@

$(BUILDDIR):
	mkdir -p $(BUILDDIR)

clean:
	rm -rf $(BUILDDIR)

# Help target
help:
	@echo "Available targets:"
	@echo "  enhanced  - Build the enhanced bank system with user management"
	@echo "  original  - Build the original step_5 system"
	@echo "  clean     - Remove build directory"
	@echo "  help      - Show this help message"

# 示例命令文件 - 用于测试增强版银行系统
# 这个文件展示了如何创建账户并进行3个月的操作记录

# 创建储蓄账户
a s S001 0.05
# 创建信用账户
a c C001 5000 0.0005 100

# 第一个月操作 (2024年1月)
d 0 1000 工资
d 0 500 奖金
w 0 200 购物
d 1 2000 转账
w 1 300 餐饮
w 0 150 交通
d 0 800 兼职收入
w 1 500 网购

# 进入下个月
n

# 第二个月操作 (2024年2月)
d 0 1200 工资
w 0 300 房租
d 1 1500 转账
w 1 800 旅游
w 0 100 电话费
d 0 600 投资收益
w 1 400 购物
w 0 250 餐饮
d 0 300 红包

# 进入下个月
n

# 第三个月操作 (2024年3月)
d 0 1100 工资
w 0 350 房租
d 1 1000 转账
w 1 600 医疗
w 0 180 保险
d 0 450 理财收益
w 1 300 教育
w 0 120 娱乐
d 0 200 礼品收入
w 1 250 维修费
